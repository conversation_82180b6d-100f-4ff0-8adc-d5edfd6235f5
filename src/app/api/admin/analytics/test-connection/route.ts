import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { BetaAnalyticsDataClient } from '@google-analytics/data';
import { getAnalyticsConfig } from '@/utils/analyticsConfig';

/**
 * GET handler for testing Google Analytics API connection
 * Returns connection status and any error details
 */
export async function GET() {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const config = getAnalyticsConfig();

    // Check configuration
    if (!config.isConfigured) {
      return NextResponse.json({
        success: false,
        error: 'Google Analytics not configured',
        details: 'Missing required environment variables',
        config: {
          hasMeasurementId: !!config.measurementId,
          hasPropertyId: !!config.propertyId,
          hasClientEmail: !!config.clientEmail,
          hasPrivateKey: !!config.privateKey,
        }
      });
    }

    if (!config.isValid) {
      return NextResponse.json({
        success: false,
        error: 'Google Analytics configured with placeholder values',
        details: 'Please update environment variables with real credentials',
        config: {
          measurementId: config.measurementId,
          propertyId: config.propertyId,
          clientEmail: config.clientEmail,
          privateKeyLength: config.privateKey.length,
        }
      });
    }

    // Test API connection
    try {
      const analyticsDataClient = new BetaAnalyticsDataClient({
        credentials: {
          client_email: config.clientEmail,
          private_key: config.privateKey.replace(/\\n/g, '\n'), // Fix newline characters
        },
        projectId: config.clientEmail.split('@')[1].split('.')[0], // Extract project ID from email
      });

      // Make a simple API call to test connection
      const [response] = await analyticsDataClient.runReport({
        property: config.propertyId,
        dateRanges: [
          {
            startDate: '7daysAgo',
            endDate: 'today',
          },
        ],
        metrics: [
          { name: 'screenPageViews' },
        ],
        limit: 1,
      });

      return NextResponse.json({
        success: true,
        message: 'Google Analytics API connection successful',
        details: {
          propertyId: config.propertyId,
          hasData: !!(response.rows && response.rows.length > 0),
          rowCount: response.rows?.length || 0,
          testTimestamp: new Date().toISOString(),
        }
      });

    } catch (apiError: any) {
      console.error('Google Analytics API Error:', apiError);

      return NextResponse.json({
        success: false,
        error: 'Google Analytics API connection failed',
        details: apiError.message || 'Unknown API error',
        errorCode: apiError.code,
        config: {
          propertyId: config.propertyId,
          clientEmail: config.clientEmail,
        }
      });
    }

  } catch (error: any) {
    console.error('Error testing Google Analytics connection:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to test connection',
      details: error.message || 'Unknown error'
    }, { status: 500 });
  }
}
